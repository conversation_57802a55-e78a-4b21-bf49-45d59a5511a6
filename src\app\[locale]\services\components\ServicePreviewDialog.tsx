"use client";

import { useState, useEffect } from "react";
import { useTranslations } from "next-intl";
import { useQueryStates } from "nuqs";
import Image from "next/image";
import { servicesData } from "../data";
import { X } from "lucide-react";
import { cn } from "@/lib/utils";
import {
  Dialog,
  DialogClose,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button, buttonVariants } from "@/components/ui/button";
import {
  Carousel,
  CarouselContent,
  CarouselItem,
  CarouselNext,
  CarouselPrevious,
} from "@/components/ui/carousel";

import LightGallery from "lightgallery/react";

// import styles
import "lightgallery/css/lightgallery.css";
import "lightgallery/css/lg-zoom.css";
import "lightgallery/css/lg-thumbnail.css";

// import plugins if you need
import lgThumbnail from "lightgallery/plugins/thumbnail";
import lgZoom from "lightgallery/plugins/zoom";

import { serviceSearchParams, serviceUrlKeys } from "../searchParams";

type Props = {
  className?: string;
};

const ServicePreviewDialog = ({ className }: Props) => {
  const t = useTranslations("services");
  const tCommon = useTranslations("common");
  const [{ selectedService }, setPreviewKey] = useQueryStates(
    serviceSearchParams,
    {
      urlKeys: serviceUrlKeys,
    },
  );

  const handleClose = () => {
    setPreviewKey({ selectedService: null });
  };

  const isOpen = Boolean(selectedService);

  const currentService = servicesData.find(
    (service) => service.key === selectedService,
  );

  const title = currentService ? t(currentService.titleKey) : "";
  const description = currentService ? t(currentService.descriptionKey) : "";

  const [currentImageIndex, setCurrentImageIndex] = useState(0);

  const handleThumbnailClick = (index: number) => {
    setCurrentImageIndex(index);
  };

  // Reset image index when service changes
  useEffect(() => {
    setCurrentImageIndex(0);
  }, [selectedService]);

  if (!currentService) {
    return null;
  }

  return (
    <>
      <Dialog
        defaultOpen={isOpen}
        onOpenChange={(isOpen) => !isOpen && handleClose()}
      >
        <DialogContent
          className={cn(
            "max-h-[90vh] w-full max-w-4xl overflow-hidden p-0",
            className,
          )}
          showCloseButton={false}
        >
          <div className="relative flex h-full flex-col">
            <DialogClose
              className={cn(
                buttonVariants({ variant: "ghost", size: "icon" }),
                "absolute top-4 right-4 z-10 bg-black/20 text-white hover:bg-black/40",
              )}
              onClick={handleClose}
            >
              <X className="h-4 w-4" />
              <span className="sr-only">{tCommon("close")}</span>
            </DialogClose>

            <button
              title={tCommon("viewFullImage")}
              className="relative aspect-[16/10] overflow-hidden"
            >
              <Image
                src={currentService.images[currentImageIndex]}
                alt={`${title}`}
                fill
                className="object-cover transition-transform duration-300 hover:scale-105"
              />
            </button>

            {currentService.images.length > 1 && (
              <div className="bg-neutral-50 px-6 py-4 dark:bg-neutral-900">
                <Carousel className="mx-auto w-full max-w-xs">
                  <CarouselContent className="-ml-1">
                    {currentService.images.map((image, index) => (
                      <CarouselItem key={index} className="basis-1/4 pl-1">
                        <button
                          onClick={() => handleThumbnailClick(index)}
                          className={cn(
                            "relative h-12 w-full overflow-hidden rounded-md border-2 transition-all",
                            index === currentImageIndex
                              ? "border-primary ring-primary/20 ring-2"
                              : "border-transparent hover:border-neutral-300",
                          )}
                        >
                          <Image
                            src={image}
                            alt={`${title} ${index + 1}`}
                            fill
                            className="object-cover"
                          />
                        </button>
                      </CarouselItem>
                    ))}
                  </CarouselContent>
                  <CarouselPrevious />
                  <CarouselNext />
                </Carousel>
              </div>
            )}

            <div className="flex-1 p-5">
              <DialogHeader>
                <DialogTitle className="text-left text-2xl font-bold">
                  {title}
                </DialogTitle>
              </DialogHeader>
              <p className="text-muted-foreground mt-4 leading-relaxed">
                {description}
              </p>
            </div>
          </div>
        </DialogContent>
      </Dialog>
      <LightGallery
        plugins={[lgThumbnail, lgZoom]}
      >
        <a
          href={currentService.images[currentImageIndex].src}
          data-src={currentService.images[currentImageIndex].src}
          title={tCommon("viewFullImage")}
        >
          <Image
            src={currentService.images[currentImageIndex]}
            alt={`${title}`}
            fill
            className="object-cover transition-transform duration-300 hover:scale-105"
          />
        </a>
      </LightGallery>
    </>
  );
};

export default ServicePreviewDialog;
